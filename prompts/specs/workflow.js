export const WORKFLOW = (projectDescription) => `
# How to Respond to This Prompt

Generate the output by following the [prompt] below.
The output must be a **single valid JSON object** starting with \`\`\`json and ending with \`\`\`.
No extra text or wrappers are allowed.

## Output Schema
\`\`\`json
{
  "workflow": [
    {
      "step": 1,
      "script": "project",
      "status": "completed",
      "description": "Generate project configuration"
    },
    {
      "step": 2,
      "script": "feature",
      "status": "pending",
      "description": "Generate feature configuration"
    },
    {
      "step": 3,
      "script": "screen",
      "status": "pending",
      "description": "Generate screen configuration"
    }
    // Optional scripts to follow depending on project description
  ]
}
\`\`\`

# Prompt

## Role:
Workflow Orchestrator

## Task:
Create a workflow JSON that defines the ordered execution of scripts required to process the project.
The JSON must include **all mandatory scripts first** (\`project\`, \`feature\`, \`screen\`) followed by any **optional scripts** inferred from the [user project description].

### Guidelines
- Each step must include:
  - **step**: execution order (integer, starts at 1).
  - **script**: the script name to call.
  - **status**: one of \`pending\`, \`running\`, \`completed\`. Default is \`pending\`.
  - **description**: short explanation of what the script does.
- Always include the mandatory scripts first:
  1. **project** → initialize project.
  2. **feature** → detect or generate features.
  3. **screen** → detect or generate screens.
- After mandatory steps, list any optional scripts that may apply (based on [user project description]).
- The workflow must remain **minimal and executable end-to-end**.
- If a script has dependencies, ensure it comes after its required step.
- Avoid redundant steps or scripts that do not directly contribute to the execution flow.

## Instructions:
- Infer the **necessary optional scripts** from the project description.
- Place them after the mandatory ones, keeping logical execution order.
- If no optional scripts are relevant, return only the mandatory steps.
- Keep statuses as \`pending\` (they will be updated dynamically at runtime).

## Additional Information:

[User Project Description]:
"""${projectDescription}"""
`;
